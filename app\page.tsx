"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  Gift,
  Package,
  Gem,
  Star,
  TrendingUp,
  Shield,
  Clock,
  UserPlus,
  CheckCircle,
  ArrowRight,
  MessageCircle,
  Lock,
  Zap,
  Award,
  Heart,
} from "lucide-react"
import { SkinsIcon } from "@/components/icons/SkinsIcon"
import { TftIcon } from "@/components/icons/TftIcon"
import { ChestsIcon } from "@/components/icons/ChestsIcon"
import { ChampionsIcon } from "@/components/icons/ChampionsIcon"
import Image from "next/image"

export default function HomePage() {

  const [searchQuery, setSearchQuery] = useState("")

  const categories = [
    { name: "Skins", icon: SkinsIcon, count: "2,847", color: "bg-purple-500" },
    { name: "Pass<PERSON>", icon: Shield, count: "156", color: "bg-violet-500" },
    { name: "Chests", icon: ChestsIcon, count: "89", color: "bg-fuchsia-500" },
    { name: "Orbs", icon: Gem, count: "234", color: "bg-pink-500" },
    { name: "Bundles", icon: Gift, count: "45", color: "bg-indigo-500" },
    { name: "TFT", icon: TftIcon, count: "178", color: "bg-amber-500" },
    { name: "Chromas", icon: TrendingUp, count: "1,234", color: "bg-emerald-500" },
    { name: "Champions", icon: ChampionsIcon, count: "167", color: "bg-blue-500" },
  ]

  const featuredItems = [
    {
      id: 1,
      name: "Legendary Dragon Skin",
      category: "Skins",
      price: "$15.99",
      originalPrice: "$24.99",
      discount: "36%",
      image: "/placeholder.svg?height=200&width=300",
      rarity: "Legendary",
    },
    {
      id: 2,
      name: "Battle Pass Premium",
      category: "Passes",
      price: "$9.99",
      originalPrice: "$14.99",
      discount: "33%",
      image: "/placeholder.svg?height=200&width=300",
      rarity: "Premium",
    },
    {
      id: 3,
      name: "Mythic Chest Bundle",
      category: "Chests",
      price: "$29.99",
      originalPrice: "$39.99",
      discount: "25%",
      image: "/placeholder.svg?height=200&width=300",
      rarity: "Mythic",
    },
  ]

  const howItWorksSteps = [
    {
      step: 1,
      title: "Choose & Purchase",
      description: "Browse our collection and purchase the items you want for yourself or as a gift",
      icon: Package,
      color: "bg-purple-600",
    },
    {
      step: 2,
      title: "Friend Request Sent",
      description: "We'll send you a friend request on your League of Legends account",
      icon: UserPlus,
      color: "bg-violet-600",
    },
    {
      step: 3,
      title: "7-Day Wait Period",
      description: "Due to Riot's friending policies, we need to wait 7 days before gifting",
      icon: Clock,
      color: "bg-fuchsia-600",
    },
    {
      step: 4,
      title: "Receive Your Items",
      description: "After the waiting period, you'll receive your purchased items directly in-game",
      icon: CheckCircle,
      color: "bg-emerald-600",
    },
  ]

  const testimonials = [
    {
      name: "Alex Chen",
      username: "@AlexGaming",
      text: "Got my dream skin in just 7 days! The process was super smooth and customer service was amazing.",
      rating: 5,
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      name: "Sarah Kim",
      username: "@SarahPlays",
      text: "Perfect for gifting friends! They were so surprised when they received the legendary skin.",
      rating: 5,
      avatar: "/placeholder.svg?height=40&width=40",
    },
    {
      name: "Mike Rodriguez",
      username: "@MikeGG",
      text: "Reliable service, fair prices, and they actually deliver. Been using them for months now.",
      rating: 5,
      avatar: "/placeholder.svg?height=40&width=40",
    },
  ]

  const faqs = [
    {
      question: "Why do I need to wait 7 days?",
      answer:
        "This is due to Riot Games' policy that requires accounts to be friends for 7 days before gifting is allowed. This helps prevent fraud and ensures account security.",
    },
    {
      question: "Is this service safe and legitimate?",
      answer:
        "Yes! We follow all of Riot's terms of service and use only legitimate gifting methods. We never ask for your account password or use any unauthorized methods.",
    },
    {
      question: "What if I don't receive my item?",
      answer:
        "We guarantee delivery of all purchased items. If there are any issues, our support team will resolve them quickly or provide a full refund.",
    },
    {
      question: "Can I gift items to friends in other regions?",
      answer:
        "Yes, we support gifting across all League of Legends regions. Just make sure to provide the correct summoner name and region during checkout.",
    },
  ]



  return (
    <div className="min-h-screen relative overflow-hidden bg-gradient-to-br from-slate-950 via-purple-950 to-slate-950">
      {/* Background Image with Blur and Color Overlay - Top 50% only */}
      <div className="absolute top-0 left-0 right-0 h-[50vh] z-0">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat filter blur-sm scale-110"
          style={{
            backgroundImage: "url('/Background.jpg')"
          }}
        />
        {/* Color overlay to maintain the original color scheme */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-950/80 via-purple-950/85 to-slate-950/80" />
        {/* Fade gradient at the bottom */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-slate-950 via-purple-950/50 to-transparent" />
      </div>
      {/* Main Content */}
      <div className="relative z-10">
        {/* Header */}
        <header className="bg-black/50 backdrop-blur-xl border-b border-purple-500/20 sticky top-0 z-40">
          <div className="flex items-center justify-between p-4">
            {/* Logo */}
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-fuchsia-600 rounded-lg flex items-center justify-center">
                <Gem className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-purple-400 to-fuchsia-400 bg-clip-text text-transparent">
                LoLVaults
              </span>
            </div>

            <div className="flex-1 max-w-2xl mx-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white w-5 h-5" />
                <Input
                  placeholder="Search for skins, passes, chests, orbs..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-white/10 border-purple-500/30 text-white placeholder-gray-400 focus:border-purple-400"
                />
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                className="border-purple-500/30 text-purple-300 hover:bg-purple-500/20 bg-transparent"
              >
                Sign In
              </Button>
              <Button className="bg-gradient-to-r from-fuchsia-600 to-pink-600 hover:from-fuchsia-700 hover:to-pink-700 text-white">
                Get Started
              </Button>
            </div>
          </div>
        </header>

        {/* Hero Section */}
        <section className="relative px-6 py-32 text-center overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-fuchsia-600/20 blur-3xl"></div>
          <div className="relative z-10 max-w-5xl mx-auto">
            <h1 className="text-6xl md:text-8xl font-bold mb-10 bg-gradient-to-r from-white via-purple-200 to-fuchsia-200 bg-clip-text text-transparent">
              Level Up Your Game
            </h1>
            <p className="text-2xl md:text-3xl text-gray-300 mb-16 max-w-3xl mx-auto">
              Discover exclusive skins, passes, chests, and orbs. Gift your friends or treat yourself to the ultimate
              gaming experience.
            </p>

            {/* Enhanced Search Bar */}
            <div className="max-w-3xl mx-auto mb-16">
              <div className="relative backdrop-blur-sm rounded-2xl">
                <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-white w-7 h-7 z-10" />
                <Input
                  placeholder="What are you looking for today?"
                  className="pl-16 pr-6 py-6 text-xl bg-white/10 border-2 border-purple-500/30 text-white placeholder-gray-400 focus:border-purple-400 rounded-2xl"
                />
              </div>
            </div>

            {/* Category Quick Access */}
            <div className="max-w-2xl mx-auto mb-16">
              <div className="flex justify-center items-center w-full gap-4">
              {categories.map((category) => (
                <Button
                  key={category.name}
                  variant="ghost"
                  size="lg"
                  className="group relative bg-transparent hover:bg-transparent border-none p-2 min-w-0"
                >
                  <div className="flex flex-col items-center gap-3">
                    <div
                      className={`w-16 h-16 ${category.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                    >
                      <category.icon className="w-7 h-7 text-white" />
                    </div>
                    <span className="text-sm text-white group-hover:text-gray-300 transition-colors font-medium truncate">
                      {category.name}
                    </span>
                  </div>
                </Button>
              ))}
              </div>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section className="px-6 py-20 bg-black/20">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">How It Works</h2>
              <p className="text-xl text-gray-300 max-w-2xl mx-auto">
                Getting your League of Legends items is simple and secure. Here's our proven 4-step process:
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {howItWorksSteps.map((step, index) => (
                <div key={step.step} className="relative">
                  <Card className="bg-white/5 backdrop-blur-sm border-purple-500/20 hover:bg-white/10 transition-all h-full">
                    <CardContent className="p-6 text-center">
                      <div
                        className={`w-16 h-16 ${step.color} rounded-2xl flex items-center justify-center mx-auto mb-4`}
                      >
                        <step.icon className="w-8 h-8 text-white" />
                      </div>
                      <div className="text-2xl font-bold text-white mb-2">Step {step.step}</div>
                      <h3 className="text-lg font-semibold text-white mb-3">{step.title}</h3>
                      <p className="text-gray-300 text-sm leading-relaxed">{step.description}</p>
                    </CardContent>
                  </Card>
                  {index < howItWorksSteps.length - 1 && (
                    <div className="hidden lg:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                      <ArrowRight className="w-8 h-8 text-purple-400" />
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div className="mt-12 text-center">
              <Card className="bg-gradient-to-r from-purple-600/10 to-fuchsia-600/10 border-purple-500/30 max-w-2xl mx-auto">
                <CardContent className="p-6">
                  <Clock className="w-12 h-12 text-fuchsia-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">Why the 7-day wait?</h3>
                  <p className="text-gray-300">
                    This waiting period is required by Riot Games' anti-fraud policies. It ensures account security and
                    prevents unauthorized gifting. We follow all official guidelines to keep your account safe!
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Featured Items */}
        <section className="px-6 py-16">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-3xl font-bold text-white">Featured Items</h2>
              <Button
                variant="outline"
                className="border-purple-500/30 text-purple-300 hover:bg-purple-500/20 bg-transparent"
              >
                View All
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredItems.map((item) => (
                <Card
                  key={item.id}
                  className="bg-white/5 backdrop-blur-sm border-purple-500/20 hover:bg-white/10 transition-all group overflow-hidden"
                >
                  <div className="relative">
                    <Image
                      src={item.image || "/placeholder.svg"}
                      alt={item.name}
                      width={300}
                      height={200}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <Badge className="absolute top-3 left-3 bg-red-500 hover:bg-red-500">-{item.discount}</Badge>
                    <Badge className="absolute top-3 right-3 bg-purple-500/80 hover:bg-purple-500/80">
                      {item.rarity}
                    </Badge>
                  </div>
                  <CardHeader>
                    <CardTitle className="text-white">{item.name}</CardTitle>
                    <CardDescription className="text-gray-400">{item.category}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-2xl font-bold text-white">{item.price}</span>
                        <span className="text-sm text-gray-400 line-through">{item.originalPrice}</span>
                      </div>
                      <Button className="bg-gradient-to-r from-fuchsia-600 to-pink-600 hover:from-fuchsia-700 hover:to-pink-700">
                        <Gift className="w-4 h-4 mr-2" />
                        Gift
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="px-6 py-20 bg-black/20">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-white mb-4">Why Choose LoLVaults?</h2>
              <p className="text-xl text-gray-300">We're the most trusted platform for League of Legends gifting</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="bg-white/5 backdrop-blur-sm border-purple-500/20 text-center">
                <CardContent className="p-8">
                  <Lock className="w-12 h-12 text-emerald-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-3">100% Secure</h3>
                  <p className="text-gray-300">
                    We follow all Riot Games policies and never compromise your account security
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-white/5 backdrop-blur-sm border-purple-500/20 text-center">
                <CardContent className="p-8">
                  <Zap className="w-12 h-12 text-purple-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-3">Fast Delivery</h3>
                  <p className="text-gray-300">Items delivered as soon as the 7-day waiting period is complete</p>
                </CardContent>
              </Card>

              <Card className="bg-white/5 backdrop-blur-sm border-purple-500/20 text-center">
                <CardContent className="p-8">
                  <Award className="w-12 h-12 text-fuchsia-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-3">Best Prices</h3>
                  <p className="text-gray-300">Competitive pricing with regular discounts and special offers</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* Testimonials Section */}
        <section className="px-6 py-16">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-white mb-4">What Our Customers Say</h2>
              <p className="text-gray-300">Join thousands of satisfied gamers who trust LoLVaults</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {testimonials.map((testimonial, index) => (
                <Card key={index} className="bg-white/5 backdrop-blur-sm border-purple-500/20">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <Image
                        src={testimonial.avatar || "/placeholder.svg"}
                        alt={testimonial.name}
                        width={40}
                        height={40}
                        className="rounded-full mr-3"
                      />
                      <div>
                        <div className="font-semibold text-white">{testimonial.name}</div>
                        <div className="text-sm text-gray-400">{testimonial.username}</div>
                      </div>
                    </div>
                    <div className="flex mb-3">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <p className="text-gray-300 italic">"{testimonial.text}"</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="px-6 py-20 bg-black/20">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-white mb-4">Frequently Asked Questions</h2>
              <p className="text-gray-300">Everything you need to know about our service</p>
            </div>

            <div className="space-y-4">
              {faqs.map((faq, index) => (
                <Card key={index} className="bg-white/5 backdrop-blur-sm border-purple-500/20">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
                      <MessageCircle className="w-5 h-5 text-purple-400 mr-2" />
                      {faq.question}
                    </h3>
                    <p className="text-gray-300 leading-relaxed">{faq.answer}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="px-6 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-white mb-12">Trusted by Gamers Worldwide</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div>
                <div className="text-4xl font-bold text-purple-400 mb-2">1M+</div>
                <div className="text-gray-400">Items Gifted</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-violet-400 mb-2">500K+</div>
                <div className="text-gray-400">Happy Gamers</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-fuchsia-400 mb-2">3,326</div>
                <div className="text-gray-400">Unique Items</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-emerald-400 mb-2">24/7</div>
                <div className="text-gray-400">Support</div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="px-6 py-20 bg-gradient-to-r from-purple-600/10 to-fuchsia-600/10">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl font-bold text-white mb-6">Ready to Level Up?</h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of gamers who trust LoLVaults for their League of Legends items
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-gradient-to-r from-fuchsia-600 to-pink-600 hover:from-fuchsia-700 hover:to-pink-700 text-white px-8 py-3"
              >
                <Heart className="w-5 h-5 mr-2" />
                Start Shopping
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-purple-500/30 text-purple-300 hover:bg-purple-500/20 bg-transparent px-8 py-3"
              >
                Learn More
              </Button>
            </div>
          </div>
        </section>
      </div>


    </div>
  )
}
