import React from 'react'

interface TftIconProps {
  className?: string
  width?: number
  height?: number
}

export const TftIcon: React.FC<TftIconProps> = ({ 
  className = "", 
  width = 18, 
  height = 18 
}) => {
  return (
    <svg 
      width={width} 
      height={height} 
      viewBox="0 0 18 18" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g id="TFT - ICON (NEW)">
        <path 
          id="Vector" 
          d="M13.1923 8.94959C12.4543 8.49923 12.6865 8.81459 13.0005 9.11015C14.3202 10.3518 15.1907 11.5733 15.5151 12.6277C15.7267 13.3164 15.5385 13.7351 15.222 13.1324C14.7414 12.2177 14.1096 11.5168 13.7255 11.2086C13.3439 10.9026 13.3504 11.0491 13.5343 11.2946C14.2857 12.2969 14.7313 13.3074 14.2849 14.089C13.552 15.372 10.5928 15.0466 7.68865 13.37C3.99074 11.2349 1.70834 8.31455 2.54282 6.81983C2.90714 6.16787 3.89786 5.89787 5.06822 5.99579C5.31698 6.01667 5.3033 5.93819 5.08262 5.88023C3.68582 5.51339 2.0399 5.85575 1.45454 6.87239C0.387136 8.72675 2.87078 12.163 7.00681 14.5512C7.88305 15.057 9.02641 15.6215 10.3782 16.0733C11.1965 16.3469 11.0791 16.6763 9.67297 16.4038C8.17717 16.114 6.31597 15.3374 4.55702 14.3039C4.0487 14.0051 3.98246 14.1239 4.35902 14.4929C4.70786 14.8345 5.37242 15.3572 6.17522 15.8353C10.2814 18.2808 15.4038 18.9184 16.8751 16.3649C18.1917 14.08 15.8286 10.5584 13.1923 8.94959Z" 
          fill="currentColor"
        />
        <path 
          id="Vector_2" 
          d="M0 0L2.214 3.83508H6.80832V11.4073C7.965 12.3412 9.88632 13.3279 11.1913 13.4147V3.83508H15.786L18 0H0Z" 
          fill="currentColor"
        />
      </g>
    </svg>
  )
}
