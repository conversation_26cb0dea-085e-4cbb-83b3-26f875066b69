"use client"

import * as React from "react"
import * as SliderPrimitive from "@radix-ui/react-slider"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"

interface PriceFilterProps {
  min?: number
  max?: number
  step?: number
  value: [number, number]
  onValueChange: (value: [number, number]) => void
  onReset: () => void
  className?: string
}

export function PriceFilter({
  min = 0,
  max = 200,
  step = 1,
  value,
  onValueChange,
  onReset,
  className,
}: PriceFilterProps) {
  const [localValue, setLocalValue] = React.useState(value)
  const [inputValues, setInputValues] = React.useState({
    min: value[0].toString(),
    max: value[1].toString(),
  })

  // Update local state when prop changes
  React.useEffect(() => {
    setLocalValue(value)
    setInputValues({
      min: value[0].toString(),
      max: value[1].toString(),
    })
  }, [value])

  const handleSliderChange = (newValue: number[]) => {
    const typedValue = newValue as [number, number]
    setLocalValue(typedValue)
    setInputValues({
      min: typedValue[0].toString(),
      max: typedValue[1].toString(),
    })
    onValueChange(typedValue)
  }

  const handleInputChange = (type: 'min' | 'max', inputValue: string) => {
    // Allow empty string and numbers only
    if (inputValue === '' || /^\d+$/.test(inputValue)) {
      setInputValues(prev => ({
        ...prev,
        [type]: inputValue
      }))
    }
  }

  const handleInputBlur = (type: 'min' | 'max') => {
    const inputValue = inputValues[type]
    let numValue = inputValue === '' ? (type === 'min' ? min : max) : parseInt(inputValue)
    
    // Clamp values to bounds
    numValue = Math.max(min, Math.min(max, numValue))
    
    let newValue: [number, number]
    if (type === 'min') {
      newValue = [numValue, Math.max(numValue, localValue[1])]
    } else {
      newValue = [Math.min(numValue, localValue[0]), numValue]
    }
    
    setLocalValue(newValue)
    setInputValues({
      min: newValue[0].toString(),
      max: newValue[1].toString(),
    })
    onValueChange(newValue)
  }

  const handleKeyDown = (e: React.KeyboardEvent, type: 'min' | 'max') => {
    if (e.key === 'Enter') {
      handleInputBlur(type)
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header with Reset */}
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-gray-300">Price Range</span>
        <button
          onClick={onReset}
          className="text-xs text-purple-400 hover:text-purple-300 transition-colors font-medium"
        >
          Reset
        </button>
      </div>

      {/* Dual Thumb Slider */}
      <div className="px-2 py-4">
        <SliderPrimitive.Root
          className="relative flex w-full touch-none select-none items-center"
          value={localValue}
          onValueChange={handleSliderChange}
          max={max}
          min={min}
          step={step}
        >
          <SliderPrimitive.Track className="relative h-1.5 w-full grow overflow-hidden rounded-full bg-white/10">
            <SliderPrimitive.Range className="absolute h-full bg-gradient-to-r from-purple-500 to-fuchsia-500 rounded-full" />
          </SliderPrimitive.Track>
          <SliderPrimitive.Thumb className="block h-4 w-4 rounded-full border border-purple-400/50 bg-white shadow-lg ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-purple-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-purple-50 hover:border-purple-400" />
          <SliderPrimitive.Thumb className="block h-4 w-4 rounded-full border border-purple-400/50 bg-white shadow-lg ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-purple-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-purple-50 hover:border-purple-400" />
        </SliderPrimitive.Root>
      </div>

      {/* Input Fields */}
      <div className="flex items-center space-x-3">
        <div className="flex-1">
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm font-medium">
              $
            </span>
            <Input
              type="text"
              value={inputValues.min}
              onChange={(e) => handleInputChange('min', e.target.value)}
              onBlur={() => handleInputBlur('min')}
              onKeyDown={(e) => handleKeyDown(e, 'min')}
              className="pl-6 h-9 bg-white/5 border-purple-500/30 text-white text-sm font-medium focus:border-purple-400 focus:ring-1 focus:ring-purple-400"
              placeholder="0"
            />
          </div>
        </div>
        
        <div className="flex-shrink-0">
          <span className="text-gray-400 font-medium">—</span>
        </div>
        
        <div className="flex-1">
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm font-medium">
              $
            </span>
            <Input
              type="text"
              value={inputValues.max}
              onChange={(e) => handleInputChange('max', e.target.value)}
              onBlur={() => handleInputBlur('max')}
              onKeyDown={(e) => handleKeyDown(e, 'max')}
              className="pl-6 h-9 bg-white/5 border-purple-500/30 text-white text-sm font-medium focus:border-purple-400 focus:ring-1 focus:ring-purple-400"
              placeholder="200"
            />
          </div>
        </div>
      </div>
    </div>
  )
}
