"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Check, ChevronsUpDown, X, Menu, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import { PriceFilter } from "@/components/ui/price-filter"

interface StoreClientProps {
  initialItems?: any[] // You can type this properly when you have actual item data
}

export function StoreClient({ initialItems = [] }: StoreClientProps) {
  // UI states
  const [sidebarOpen, setSidebarOpen] = useState(false)

  // Filter states
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 200])
  const [selectedType, setSelectedType] = useState("")
  const [selectedRarity, setSelectedRarity] = useState("")
  const [selectedSort, setSelectedSort] = useState("")
  const [typeOpen, setTypeOpen] = useState(false)
  const [rarityOpen, setRarityOpen] = useState(false)
  const [sortOpen, setSortOpen] = useState(false)

  // Filter options
  const typeOptions = [
    { value: "skins", label: "Skins" },
    { value: "passes", label: "Passes" },
    { value: "chests", label: "Chests" },
    { value: "orbs", label: "Orbs" },
    { value: "bundles", label: "Bundles" },
    { value: "tft", label: "TFT" },
    { value: "chromas", label: "Chromas" },
    { value: "champions", label: "Champions" },
  ]

  const rarityOptions = [
    { value: "common", label: "Common" },
    { value: "uncommon", label: "Uncommon" },
    { value: "rare", label: "Rare" },
    { value: "epic", label: "Epic" },
    { value: "legendary", label: "Legendary" },
    { value: "mythic", label: "Mythic" },
  ]

  const sortOptions = [
    { value: "price-high", label: "Highest Price" },
    { value: "price-low", label: "Lowest Price" },
  ]

  const clearFilters = () => {
    setPriceRange([0, 200])
    setSelectedType("")
    setSelectedRarity("")
    setSelectedSort("")
  }

  const resetPriceRange = () => {
    setPriceRange([0, 200])
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-purple-950 to-slate-950">
      {/* Header */}
      <header className="border-b border-purple-500/20 bg-black/20 backdrop-blur-sm">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="text-white hover:bg-white/10 lg:hidden"
            >
              <Menu className="h-5 w-5" />
            </Button>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-fuchsia-400 bg-clip-text text-transparent">
              LoLVaults Store
            </h1>
          </div>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search items..."
                className="pl-10 w-64 bg-white/10 border-purple-500/30 text-white placeholder:text-gray-400"
              />
            </div>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <div className={cn(
          "fixed inset-y-0 left-0 z-50 w-80 bg-black/40 backdrop-blur-md border-r border-purple-500/20 transform transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0",
          sidebarOpen ? "translate-x-0" : "-translate-x-full"
        )}>
          <div className="flex flex-col h-full">
            <div className="p-6 border-b border-purple-500/20">
              <h2 className="text-lg font-semibold text-white mb-4">Filters</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(false)}
                className="absolute top-4 right-4 text-gray-400 hover:text-white lg:hidden"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
            
            <div className="flex-1 overflow-y-auto p-6">
              <div className="space-y-6">
                {/* Price Filter */}
                <PriceFilter
                  value={priceRange}
                  onValueChange={setPriceRange}
                  onReset={resetPriceRange}
                  min={0}
                  max={200}
                  step={1}
                />

                {/* Type Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-300 mb-2 block">
                    Type
                  </label>
                  <Popover open={typeOpen} onOpenChange={setTypeOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={typeOpen}
                        className="w-full justify-between bg-white/10 border-purple-500/30 text-white hover:bg-white/20"
                      >
                        {selectedType
                          ? typeOptions.find((option) => option.value === selectedType)?.label
                          : "Select type..."}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0 bg-black/90 border-purple-500/30">
                      <Command className="bg-black/90 text-white [&_[cmdk-input-wrapper]]:border-purple-500/30 [&_[cmdk-input]]:text-white [&_[cmdk-input]]:placeholder:text-gray-400">
                        <CommandInput placeholder="Search type..." className="text-white placeholder:text-gray-400 border-purple-500/30" />
                        <CommandList className="bg-black/90">
                          <CommandEmpty className="text-gray-400">No type found.</CommandEmpty>
                          <CommandGroup>
                            {typeOptions.map((option) => (
                              <CommandItem
                                key={option.value}
                                value={option.value}
                                onSelect={(currentValue) => {
                                  setSelectedType(currentValue === selectedType ? "" : currentValue)
                                  setTypeOpen(false)
                                }}
                                className="text-white hover:bg-purple-500/20 data-[selected=true]:bg-purple-500/20"
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4 text-white",
                                    selectedType === option.value ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                {option.label}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Rarity Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-300 mb-2 block">
                    Rarity
                  </label>
                  <Popover open={rarityOpen} onOpenChange={setRarityOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={rarityOpen}
                        className="w-full justify-between bg-white/10 border-purple-500/30 text-white hover:bg-white/20"
                      >
                        {selectedRarity
                          ? rarityOptions.find((option) => option.value === selectedRarity)?.label
                          : "Select rarity..."}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0 bg-black/90 border-purple-500/30">
                      <Command className="bg-black/90 text-white [&_[cmdk-input-wrapper]]:border-purple-500/30 [&_[cmdk-input]]:text-white [&_[cmdk-input]]:placeholder:text-gray-400">
                        <CommandInput placeholder="Search rarity..." className="text-white placeholder:text-gray-400 border-purple-500/30" />
                        <CommandList className="bg-black/90">
                          <CommandEmpty className="text-gray-400">No rarity found.</CommandEmpty>
                          <CommandGroup>
                            {rarityOptions.map((option) => (
                              <CommandItem
                                key={option.value}
                                value={option.value}
                                onSelect={(currentValue) => {
                                  setSelectedRarity(currentValue === selectedRarity ? "" : currentValue)
                                  setRarityOpen(false)
                                }}
                                className="text-white hover:bg-purple-500/20 data-[selected=true]:bg-purple-500/20"
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4 text-white",
                                    selectedRarity === option.value ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                {option.label}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Sort By Filter */}
                <div>
                  <label className="text-sm font-medium text-gray-300 mb-2 block">
                    Sort By
                  </label>
                  <Popover open={sortOpen} onOpenChange={setSortOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={sortOpen}
                        className="w-full justify-between bg-white/10 border-purple-500/30 text-white hover:bg-white/20"
                      >
                        {selectedSort
                          ? sortOptions.find((option) => option.value === selectedSort)?.label
                          : "Select sort..."}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-full p-0 bg-black/90 border-purple-500/30">
                      <Command className="bg-black/90 text-white [&_[cmdk-input-wrapper]]:border-purple-500/30 [&_[cmdk-input]]:text-white [&_[cmdk-input]]:placeholder:text-gray-400">
                        <CommandInput placeholder="Search sort..." className="text-white placeholder:text-gray-400 border-purple-500/30" />
                        <CommandList className="bg-black/90">
                          <CommandEmpty className="text-gray-400">No sort option found.</CommandEmpty>
                          <CommandGroup>
                            {sortOptions.map((option) => (
                              <CommandItem
                                key={option.value}
                                value={option.value}
                                onSelect={(currentValue) => {
                                  setSelectedSort(currentValue === selectedSort ? "" : currentValue)
                                  setSortOpen(false)
                                }}
                                className="text-white hover:bg-purple-500/20 data-[selected=true]:bg-purple-500/20"
                              >
                                <Check
                                  className={cn(
                                    "mr-2 h-4 w-4 text-white",
                                    selectedSort === option.value ? "opacity-100" : "opacity-0"
                                  )}
                                />
                                {option.label}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Clear Filters Button */}
                <Button
                  variant="outline"
                  onClick={clearFilters}
                  className="w-full border-purple-500/30 text-purple-300 hover:bg-purple-500/20 bg-transparent"
                >
                  Clear All Filters
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 lg:ml-0">
          <div className="p-6">
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-white mb-2">
                League of Legends Store
              </h1>
              <p className="text-xl text-gray-300">
                Browse our collection of League of Legends items
              </p>
            </div>

            {/* Active Filters Display */}
            {(priceRange[0] !== 0 || priceRange[1] !== 200 || selectedType || selectedRarity || selectedSort) && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-300 mb-2">Active Filters:</h3>
                <div className="flex flex-wrap gap-2">
                  {(priceRange[0] !== 0 || priceRange[1] !== 200) && (
                    <Badge variant="secondary" className="bg-purple-500/20 text-purple-300">
                      Price: ${priceRange[0]} - ${priceRange[1]}
                      <button
                        onClick={resetPriceRange}
                        className="ml-2 hover:text-white"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  )}
                  {selectedType && (
                    <Badge variant="secondary" className="bg-purple-500/20 text-purple-300">
                      Type: {typeOptions.find(t => t.value === selectedType)?.label}
                      <button
                        onClick={() => setSelectedType("")}
                        className="ml-2 hover:text-white"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  )}
                  {selectedRarity && (
                    <Badge variant="secondary" className="bg-purple-500/20 text-purple-300">
                      Rarity: {rarityOptions.find(r => r.value === selectedRarity)?.label}
                      <button
                        onClick={() => setSelectedRarity("")}
                        className="ml-2 hover:text-white"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  )}
                  {selectedSort && (
                    <Badge variant="secondary" className="bg-purple-500/20 text-purple-300">
                      Sort: {sortOptions.find(s => s.value === selectedSort)?.label}
                      <button
                        onClick={() => setSelectedSort("")}
                        className="ml-2 hover:text-white"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Store Items Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {initialItems.length === 0 ? (
                <div className="col-span-full">
                  <Card className="p-12 text-center bg-white/5 border-purple-500/20">
                    <h3 className="text-2xl font-semibold text-white mb-2">
                      Store Coming Soon
                    </h3>
                    <p className="text-gray-400">
                      We're working hard to bring you the best League of Legends items. Check back soon!
                    </p>
                  </Card>
                </div>
              ) : (
                initialItems.map((item, index) => (
                  <Card key={index} className="bg-white/5 border-purple-500/20 hover:bg-white/10 transition-colors">
                    {/* Item content will go here when you have actual items */}
                    <div className="p-4">
                      <h3 className="text-white font-semibold">{item.name}</h3>
                      <p className="text-gray-400">${item.price}</p>
                    </div>
                  </Card>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Overlay for mobile sidebar */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  )
}
