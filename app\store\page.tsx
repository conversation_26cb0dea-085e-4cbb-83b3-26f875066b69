"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Search,
  Filter,
  X,
  Gem,
  SlidersHorizontal,
} from "lucide-react"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { PriceFilter } from "@/components/ui/price-filter"

export default function StorePage() {
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  
  // Filter states
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 200])
  const [selectedType, setSelectedType] = useState("")
  const [selectedRarity, setSelectedRarity] = useState("")
  const [selectedSort, setSelectedSort] = useState("")
  const [typeOpen, setTypeOpen] = useState(false)
  const [rarityOpen, setRarityOpen] = useState(false)
  const [sortOpen, setSortOpen] = useState(false)

  // Filter options
  const typeOptions = [
    { value: "skins", label: "Skins" },
    { value: "passes", label: "Passes" },
    { value: "chests", label: "Chests" },
    { value: "orbs", label: "Orbs" },
    { value: "bundles", label: "Bundles" },
    { value: "tft", label: "TFT" },
    { value: "chromas", label: "Chromas" },
    { value: "champions", label: "Champions" },
  ]

  const rarityOptions = [
    { value: "common", label: "Common" },
    { value: "uncommon", label: "Uncommon" },
    { value: "rare", label: "Rare" },
    { value: "epic", label: "Epic" },
    { value: "legendary", label: "Legendary" },
    { value: "mythic", label: "Mythic" },
  ]

  const sortOptions = [
    { value: "price-high", label: "Highest Price" },
    { value: "price-low", label: "Lowest Price" },
  ]

  const clearFilters = () => {
    setPriceRange([0, 200])
    setSelectedType("")
    setSelectedRarity("")
    setSelectedSort("")
  }

  const resetPriceRange = () => {
    setPriceRange([0, 200])
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-950 via-purple-950 to-slate-950">
      {/* Header */}
      <header className="bg-black/50 backdrop-blur-xl border-b border-purple-500/20 sticky top-0 z-40">
        <div className="flex items-center justify-between p-4">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-fuchsia-600 rounded-lg flex items-center justify-center">
              <Gem className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-purple-400 to-fuchsia-400 bg-clip-text text-transparent">
              LoLVaults
            </span>
          </div>

          <div className="flex-1 max-w-2xl mx-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white w-5 h-5" />
              <Input
                placeholder="Search for skins, passes, chests, orbs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-white/10 border-purple-500/30 text-white placeholder-gray-400 focus:border-purple-400"
              />
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              className="border-purple-500/30 text-purple-300 hover:bg-purple-500/20 bg-transparent"
            >
              Sign In
            </Button>
            <Button className="bg-gradient-to-r from-fuchsia-600 to-pink-600 hover:from-fuchsia-700 hover:to-pink-700 text-white">
              Get Started
            </Button>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <div
          className={`fixed inset-y-0 left-0 z-30 w-80 bg-black/90 backdrop-blur-xl border-r border-purple-500/20 transform transition-transform duration-300 ease-in-out ${
            sidebarOpen ? "translate-x-0" : "-translate-x-full"
          } top-[73px]`}
        >
          <div className="p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-white flex items-center">
                <SlidersHorizontal className="w-5 h-5 mr-2" />
                Filters
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(false)}
                className="text-gray-400 hover:text-white"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            <div className="space-y-6">
              {/* Price Filter */}
              <PriceFilter
                value={priceRange}
                onValueChange={setPriceRange}
                onReset={resetPriceRange}
                min={0}
                max={200}
                step={1}
              />

              {/* Type Filter */}
              <div>
                <label className="text-sm font-medium text-gray-300 mb-2 block">
                  Type
                </label>
                <Popover open={typeOpen} onOpenChange={setTypeOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={typeOpen}
                      className="w-full justify-between bg-white/10 border-purple-500/30 text-white hover:bg-white/20"
                    >
                      {selectedType
                        ? typeOptions.find((option) => option.value === selectedType)?.label
                        : "Select type..."}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0 bg-black/90 border-purple-500/30">
                    <Command className="bg-black/90 text-white [&_[cmdk-input-wrapper]]:border-purple-500/30 [&_[cmdk-input]]:text-white [&_[cmdk-input]]:placeholder:text-gray-400">
                      <CommandInput placeholder="Search type..." className="text-white placeholder:text-gray-400 border-purple-500/30" />
                      <CommandList className="bg-black/90">
                        <CommandEmpty className="text-gray-400">No type found.</CommandEmpty>
                        <CommandGroup>
                          {typeOptions.map((option) => (
                            <CommandItem
                              key={option.value}
                              value={option.value}
                              onSelect={(currentValue) => {
                                setSelectedType(currentValue === selectedType ? "" : currentValue)
                                setTypeOpen(false)
                              }}
                              className="text-white hover:bg-purple-500/20 data-[selected=true]:bg-purple-500/20"
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4 text-white",
                                  selectedType === option.value ? "opacity-100" : "opacity-0"
                                )}
                              />
                              {option.label}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>

              {/* Rarity Filter */}
              <div>
                <label className="text-sm font-medium text-gray-300 mb-2 block">
                  Rarity
                </label>
                <Popover open={rarityOpen} onOpenChange={setRarityOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={rarityOpen}
                      className="w-full justify-between bg-white/10 border-purple-500/30 text-white hover:bg-white/20"
                    >
                      {selectedRarity
                        ? rarityOptions.find((option) => option.value === selectedRarity)?.label
                        : "Select rarity..."}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0 bg-black/90 border-purple-500/30">
                    <Command className="bg-black/90 text-white [&_[cmdk-input-wrapper]]:border-purple-500/30 [&_[cmdk-input]]:text-white [&_[cmdk-input]]:placeholder:text-gray-400">
                      <CommandInput placeholder="Search rarity..." className="text-white placeholder:text-gray-400 border-purple-500/30" />
                      <CommandList className="bg-black/90">
                        <CommandEmpty className="text-gray-400">No rarity found.</CommandEmpty>
                        <CommandGroup>
                          {rarityOptions.map((option) => (
                            <CommandItem
                              key={option.value}
                              value={option.value}
                              onSelect={(currentValue) => {
                                setSelectedRarity(currentValue === selectedRarity ? "" : currentValue)
                                setRarityOpen(false)
                              }}
                              className="text-white hover:bg-purple-500/20 data-[selected=true]:bg-purple-500/20"
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4 text-white",
                                  selectedRarity === option.value ? "opacity-100" : "opacity-0"
                                )}
                              />
                              {option.label}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>

              {/* Sort By Filter */}
              <div>
                <label className="text-sm font-medium text-gray-300 mb-2 block">
                  Sort By
                </label>
                <Popover open={sortOpen} onOpenChange={setSortOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={sortOpen}
                      className="w-full justify-between bg-white/10 border-purple-500/30 text-white hover:bg-white/20"
                    >
                      {selectedSort
                        ? sortOptions.find((option) => option.value === selectedSort)?.label
                        : "Select sort..."}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0 bg-black/90 border-purple-500/30">
                    <Command className="bg-black/90 text-white [&_[cmdk-input-wrapper]]:border-purple-500/30 [&_[cmdk-input]]:text-white [&_[cmdk-input]]:placeholder:text-gray-400">
                      <CommandInput placeholder="Search sort..." className="text-white placeholder:text-gray-400 border-purple-500/30" />
                      <CommandList className="bg-black/90">
                        <CommandEmpty className="text-gray-400">No sort option found.</CommandEmpty>
                        <CommandGroup>
                          {sortOptions.map((option) => (
                            <CommandItem
                              key={option.value}
                              value={option.value}
                              onSelect={(currentValue) => {
                                setSelectedSort(currentValue === selectedSort ? "" : currentValue)
                                setSortOpen(false)
                              }}
                              className="text-white hover:bg-purple-500/20 data-[selected=true]:bg-purple-500/20"
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4 text-white",
                                  selectedSort === option.value ? "opacity-100" : "opacity-0"
                                )}
                              />
                              {option.label}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>

              {/* Type Filter */}
              <div>
                <label className="text-sm font-medium text-gray-300 mb-2 block">
                  Type
                </label>
                <Popover open={typeOpen} onOpenChange={setTypeOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={typeOpen}
                      className="w-full justify-between bg-white/10 border-purple-500/30 text-white hover:bg-white/20"
                    >
                      {selectedType
                        ? typeOptions.find((option) => option.value === selectedType)?.label
                        : "Select type..."}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0 bg-black/90 border-purple-500/30">
                    <Command className="bg-black/90 text-white [&_[cmdk-input-wrapper]]:border-purple-500/30 [&_[cmdk-input]]:text-white [&_[cmdk-input]]:placeholder:text-gray-400">
                      <CommandInput placeholder="Search type..." className="text-white placeholder:text-gray-400 border-purple-500/30" />
                      <CommandList className="bg-black/90">
                        <CommandEmpty className="text-gray-400">No type found.</CommandEmpty>
                        <CommandGroup>
                          {typeOptions.map((option) => (
                            <CommandItem
                              key={option.value}
                              value={option.value}
                              onSelect={(currentValue) => {
                                setSelectedType(currentValue === selectedType ? "" : currentValue)
                                setTypeOpen(false)
                              }}
                              className="text-white hover:bg-purple-500/20 data-[selected=true]:bg-purple-500/20"
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4 text-white",
                                  selectedType === option.value ? "opacity-100" : "opacity-0"
                                )}
                              />
                              {option.label}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>



              {/* Clear Filters Button */}
              <Button
                variant="outline"
                onClick={clearFilters}
                className="w-full border-purple-500/30 text-purple-300 hover:bg-purple-500/20 bg-transparent"
              >
                Clear All Filters
              </Button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? "ml-80" : "ml-0"}`}>
          {/* Toggle Sidebar Button */}
          {!sidebarOpen && (
            <div className="p-4">
              <Button
                variant="outline"
                onClick={() => setSidebarOpen(true)}
                className="border-purple-500/30 text-purple-300 hover:bg-purple-500/20 bg-transparent"
              >
                <Filter className="w-4 h-4 mr-2" />
                Show Filters
              </Button>
            </div>
          )}

          {/* Store Content */}
          <div className="p-6">
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-white mb-4">Store</h1>
              <p className="text-xl text-gray-300">
                Browse our collection of League of Legends items
              </p>
            </div>

            {/* Active Filters Display */}
            {(priceRange[0] !== 0 || priceRange[1] !== 200 || selectedType || selectedRarity || selectedSort) && (
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-300 mb-2">Active Filters:</h3>
                <div className="flex flex-wrap gap-2">
                  {(priceRange[0] !== 0 || priceRange[1] !== 200) && (
                    <Badge variant="secondary" className="bg-purple-500/20 text-purple-300">
                      Price: ${priceRange[0]} - ${priceRange[1]}
                      <button
                        onClick={resetPriceRange}
                        className="ml-2 hover:text-white"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  )}
                  {selectedType && (
                    <Badge variant="secondary" className="bg-purple-500/20 text-purple-300">
                      Type: {typeOptions.find(t => t.value === selectedType)?.label}
                      <button
                        onClick={() => setSelectedType("")}
                        className="ml-2 hover:text-white"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  )}
                  {selectedRarity && (
                    <Badge variant="secondary" className="bg-purple-500/20 text-purple-300">
                      Rarity: {rarityOptions.find(r => r.value === selectedRarity)?.label}
                      <button
                        onClick={() => setSelectedRarity("")}
                        className="ml-2 hover:text-white"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  )}
                  {selectedSort && (
                    <Badge variant="secondary" className="bg-purple-500/20 text-purple-300">
                      Sort: {sortOptions.find(s => s.value === selectedSort)?.label}
                      <button
                        onClick={() => setSelectedSort("")}
                        className="ml-2 hover:text-white"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Empty State */}
            <div className="text-center py-20">
              <div className="w-24 h-24 bg-gradient-to-r from-purple-600 to-fuchsia-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Gem className="w-12 h-12 text-white" />
              </div>
              <h2 className="text-2xl font-bold text-white mb-4">Store Coming Soon</h2>
              <p className="text-gray-300 max-w-md mx-auto">
                We're working hard to bring you the best League of Legends items. Check back soon!
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
