import { StoreClient } from "@/components/store/store-client"

// ISR Configuration
export const revalidate = 3600 // Revalidate every hour (3600 seconds)

// This function runs at build time and on revalidation
async function getStoreData() {
  // In a real application, you would fetch data from your API/database here
  // For now, we'll return empty data since the store is coming soon

  // Example of what this might look like:
  // const response = await fetch('https://your-api.com/store/items', {
  //   next: { revalidate: 3600 }
  // })
  // const items = await response.json()

  return {
    items: [], // Empty for now, will be populated when you have actual store items
    lastUpdated: new Date().toISOString(),
  }
}

export default async function StorePage() {
  // Fetch data at build time and on revalidation
  const storeData = await getStoreData()

  return <StoreClient initialItems={storeData.items} />
}